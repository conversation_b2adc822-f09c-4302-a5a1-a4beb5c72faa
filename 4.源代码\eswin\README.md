# 智能安全箱温度CO2监测报警系统

## 项目概述

基于ESWIN EAM2011芯片的物联网安全监测系统，集成温湿度传感器、CO2传感器，实现环境监测、报警触发和安全箱自动控制功能。系统通过WiFi模块连接华为云平台，提供远程监控能力。

## 硬件架构

### 主控芯片
- **型号**: ESWIN EAM2011
- **封装**: 100引脚

### 外设模块
| 模块 | 型号 | 接口 | 功能 |
|------|------|------|------|
| WiFi模块 | HC-25 | UART4 | 网络连接、MQTT通信 |
| 显示屏 | HF018 | UART1 | 本地状态显示 |
| 温湿度传感器 | SHT30 | UART5 | 温湿度检测 |
| CO2传感器 | 客户自配 | UART5 | CO2浓度检测 |
| 继电器模块 | - | PORTC-29 | 电磁锁控制 |
| 状态LED | LED3/LED4 | PORTC-28/PORTD-14 | 状态指示 |

### 串口分配
- **UART1**: HF018显示屏通信
- **UART2**: 调试串口
- **UART4**: HC-25 WiFi模块通信
- **UART5**: 温湿度传感器 + CO2传感器通信

## 功能需求

### 核心功能
1. **环境监测**
   - 实时采集温度、湿度、CO2浓度数据
   - 数据解析和处理

2. **报警触发**
   - 温度阈值: ≥100°C
   - CO2浓度阈值: ≥1000ppm
   - 触发条件: 温度OR CO2任一超标

3. **安全箱控制**
   - 报警触发时立即打开安全箱
   - 通过继电器控制5V电磁锁
   - 继电器高电平触发

4. **报警状态管理**
   - 报警持续时间: 6小时自动复位
   - LED闪烁指示报警状态
   - 屏幕显示报警信息

5. **数据上报**
   - 通过HC-25模块连接华为云
   - MQTT协议数据传输
   - 上报频率: 6小时/次

### 显示功能
- **正常状态**: 温度、湿度、CO2浓度、安全箱状态
- **报警状态**: 上述信息 + 报警提示信息
- **网络状态**: WiFi连接状态、IP地址

## 技术规格

### 报警逻辑
```
触发条件: (温度 ≥ 100°C) OR (CO2 ≥ 1000ppm)
触发动作:
  1. 立即打开安全箱(继电器高电平)
  2. LED开始闪烁
  3. 屏幕显示报警信息
  4. 启动6小时定时器

复位条件: 6小时定时到达
复位动作:
  1. 关闭安全箱(继电器低电平)
  2. LED停止闪烁
  3. 屏幕恢复正常显示
  4. 清除报警状态
```

### MQTT数据格式
```json
{
  "services": [
    {
      "service_id": "safetyBoxMonitor",
      "properties": {
        "temperature": 25.5,
        "humidity": 60.2,
        "co2": 800,
        "safetyBoxStatus": "closed",
        "alarmStatus": "normal"
      }
    }
  ]
}
```

### 继电器控制
- **GPIO引脚**: PORTC-29
- **驱动电压**: 5V
- **触发电平**: 高电平有效
- **负载**: 电磁锁

### LED指示
- **正常状态**: LED熄灭
- **报警状态**: LED3闪烁(1Hz频率)
- **网络状态**: LED4指示WiFi连接状态

### CO2传感器通信协议
- **接口复用**: 与SHT30传感器轮询共用UART5
- **请求指令**: `"CO2\r\n"`
- **返回格式**: `"CO2:1200ppm\r\n"`
- **轮询间隔**: 温湿度和CO2传感器间隔100ms

## 技术实现方案

### 传感器接口设计
1. **CO2传感器数据格式**
   - [x] 串口通信协议: 标准UART通信
   - [x] 数据请求指令: `"CO2\r\n"`
   - [x] 返回数据格式: `"CO2:1200ppm\r\n"`
   - [x] 与SHT30传感器的接口复用方式: 轮询方式，间隔100ms

2. **继电器控制**
   - [x] 具体GPIO引脚选择: PORTC-29
   - [x] 单路继电器控制电磁锁

### 系统行为设计
3. **定时复位机制**
   - [x] 6小时计时起始点: 报警触发时刻开始计时
   - [x] 定时到达后安全箱状态: 自动关闭安全箱
   - [x] 重复报警时定时器处理: 重新开始6小时计时

4. **LED闪烁模式**
   - [x] 闪烁LED选择: LED3用于报警指示
   - [x] 闪烁频率设定: 1Hz(1秒亮，1秒灭)

5. **屏幕显示布局**
   - [x] 显示区域分配:
     - ID 6: 温度显示 (xx.x°C)
     - ID 7: 湿度显示 (xx.x%RH)
     - ID 5: CO2浓度显示 (xxxxppm)
     - ID 4: 安全箱状态/报警信息

6. **数据上报策略**
   - [x] 6小时上报时机: 系统启动后每6小时滚动上报
   - [x] 报警时立即上报一次报警信息
   - [x] 网络断开时: 本地功能正常，不缓存数据

7. **状态持久化**
   - [x] 系统重启后: 重新开始，不保持之前状态
   - [x] 报警状态和定时器: 不做掉电保持

## 故障处理策略

### 通信故障
- **传感器通信失败**: 系统进入待机状态
- **网络断开**: 本地功能正常，无需网络依赖
- **屏幕通信失败**: 核心功能不受影响

### 系统可靠性
- **看门狗**: 不需要实现
- **错误恢复**: 基本错误处理即可
- **日志记录**: 通过UART2调试输出

## 开发环境

- **IDE**: ESWIN开发环境
- **SDK**: ESWIN_SDK
- **编译器**: ARM GCC
- **调试**: UART2串口调试

## 项目文件结构

```
eswin/
├── src/
│   └── main.c              # 主程序文件
├── board/                  # 板级配置
│   ├── clock_config.c/h    # 时钟配置
│   ├── pin_config.c/h      # 引脚配置
│   ├── peripherals_uart_config.c/h  # 串口配置
│   ├── rxcalback.c/h       # 串口回调
│   └── sdk_project_config.h # 项目配置头文件
├── ESWIN_SDK/              # SDK库文件
└── project.ect             # 项目配置文件
```

## 代码架构设计

### 新增数据结构
```c
// 系统状态枚举
typedef enum {
    SYSTEM_NORMAL,      // 正常状态
    SYSTEM_ALARM,       // 报警状态
    SYSTEM_STANDBY      // 待机状态
} system_state_t;

// 传感器数据结构
typedef struct {
    float temperature;      // 温度(°C)
    float humidity;        // 湿度(%RH)
    uint16_t co2_ppm;      // CO2浓度(ppm)
    uint32_t timestamp;    // 时间戳
} sensor_data_t;

// 报警管理结构
typedef struct {
    bool is_alarm;              // 是否处于报警状态
    uint32_t alarm_start_time;  // 报警开始时间
    uint32_t alarm_duration;    // 报警持续时间(6小时)
    bool led_state;             // LED闪烁状态
} alarm_manager_t;
```

### 主要功能模块
1. **传感器数据采集模块**
   - 扩展现有`sht30_gethumtem()`函数
   - 新增`getCO2Data()`函数
   - 实现轮询采集机制

2. **报警检测与管理模块**
   - 新增`checkAlarmCondition()`函数
   - 新增`alarmManager()`状态机
   - 实现6小时定时复位

3. **安全箱控制模块**
   - 新增`safetyBoxControl()`函数
   - 继电器GPIO控制

4. **LED状态指示模块**
   - 扩展现有LED控制
   - 实现1Hz闪烁功能

5. **屏幕显示增强模块**
   - 扩展现有屏幕显示功能
   - 新增CO2和状态显示

6. **MQTT数据上报模块**
   - 扩展现有MQTT功能
   - 新增CO2和报警状态上报

### 开发策略
- **代码复用**: 保留现有串口、MQTT、屏幕通信框架
- **功能扩展**: 在现有基础上增加新功能模块
- **向后兼容**: 保持原有功能正常工作

## 版本历史

- **v1.0**: 基础温湿度监测和MQTT上报功能
- **v2.0**: (开发中)增加CO2监测和安全箱控制功能
  - 新增CO2传感器支持
  - 新增报警检测和管理
  - 新增安全箱继电器控制
  - 新增LED闪烁报警指示
  - 增强屏幕显示功能
  - 扩展MQTT数据格式