#include "../board/sdk_project_config.h"

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

//温湿度模块报文格式
#define SHT30_HUM    "R:"
#define SHT30_TEM    "RH "
#define SHT30_PREFIX "Read\r\n"
#define data_len     10

//CO2传感器报文格式
#define CO2_PREFIX   "CO2\r\n"
#define CO2_RESPONSE "CO2:"
#define CO2_SUFFIX   "ppm"

//屏幕显示位置
#define HF018_HUM_ID    7  // 湿度显示
#define HF018_TUM_ID    6  // 温度显示
#define HF018_CO2_ID    5  // CO2浓度显示
#define HF018_STATUS_ID 4  // 安全箱状态/报警信息

//报警阈值
#define TEMP_ALARM_THRESHOLD 100.0f  // 温度报警阈值 100°C
#define CO2_ALARM_THRESHOLD  1000    // CO2报警阈值 1000ppm

//继电器控制
#define RELAY_PORT PORTC
#define RELAY_PIN  29
#define RELAY_ON   GPIO_PIN_HIGH
#define RELAY_OFF  GPIO_PIN_LOW

//报警管理
#define ALARM_DURATION_MS (6 * 3600 * 1000)  // 6小时 = 6*3600*1000ms
#define LED_BLINK_INTERVAL 1000               // LED闪烁间隔1秒
#define SENSOR_POLL_INTERVAL 100              // 传感器轮询间隔100ms
#define MQTT_REPORT_INTERVAL (6 * 3600 * 1000) // MQTT上报间隔6小时

//屏幕AT指令
#define HF018_JUMP   "JUMP(1);\r\n"
#define SEND_SUCCESS "OK"

//WIFE模块AT指令
#define HC25_CODE             "+++"
#define HC25_CODE_REPORT      "ENTERED"
#define HC25_EXIT             "AT+ENTM"
#define HC25_EXIT_REPORT      "EXITED"
#define MQTT_HC25_IP          "AT+WANN"
#define MQTT_HC25_IP_REPORT   "DHCP"
#define MQTT_HC25_SSIC        "AT+WSLK"
#define MQTT_HC25_SSIC_REPORT "Sta"

//WIFE模块AT指令报文格式
#define MQTT_IP       ","
#define MQTT_IP_LEN   100
#define MQTT_SSIC_PRE ":"
#define MQTT_SSIC_END ","
#define MQTT_SSIC_LEN 100

//LED控制
#define LED_NUMBER    "led"
#define LED_ACTION    "action"
#define GPIO_PIN_HIGH 1
#define GPIO_PIN_LOW  0

//串口接收缓冲区
uint8_t buffer[BUFFER_SIZE];
uint8_t bufferldx;

//AT指令串口接收缓冲区
char Atvuffer[BUFFER_SIZE];

//系统状态枚举
typedef enum {
    SYSTEM_NORMAL,      // 正常状态
    SYSTEM_ALARM,       // 报警状态
    SYSTEM_STANDBY      // 待机状态
} system_state_t;

//传感器数据结构
typedef struct {
    float temperature;      // 温度(°C)
    float humidity;        // 湿度(%RH)
    uint16_t co2_ppm;      // CO2浓度(ppm)
    uint32_t timestamp;    // 时间戳
} sensor_data_t;

//报警管理结构
typedef struct {
    bool is_alarm;              // 是否处于报警状态
    uint32_t alarm_start_time;  // 报警开始时间
    uint32_t alarm_duration;    // 报警持续时间(6小时)
    bool led_state;             // LED闪烁状态
    uint32_t last_led_toggle;   // 上次LED切换时间
} alarm_manager_t;

//全局变量
static system_state_t g_system_state = SYSTEM_NORMAL;
static sensor_data_t g_sensor_data = {0};
static alarm_manager_t g_alarm_manager = {false, 0, ALARM_DURATION_MS, false, 0};
static uint32_t g_last_mqtt_report = 0;
static uint32_t g_last_sensor_poll = 0;

//串口初始化
void eswin_uart_init(void)
{
    //串口引脚初始化
    UART_DRV_Init(INST_UART1_SAMPLE, &g_stUartState_1, &g_stUart1UserConfig0);
    UART_DRV_Init(INST_UART2_SAMPLE, &g_stUartState_2, &g_stUart2UserConfig0);
    UART_DRV_Init(INST_UART4_SAMPLE, &g_stUartState_4, &g_stUart4UserConfig0);
    UART_DRV_Init(INST_UART5_SAMPLE, &g_stUartState_5, &g_stUart5UserConfig0);
    //设置接收回调函数
    UART_DRV_InstallRxCallback(INST_UART1_SAMPLE, RxCallback1, NULL);
    UART_DRV_InstallRxCallback(INST_UART2_SAMPLE, RxCallback2, NULL);
    UART_DRV_InstallRxCallback(INST_UART4_SAMPLE, RxCallback4, NULL);
    UART_DRV_InstallRxCallback(INST_UART5_SAMPLE, RxCallback5, NULL);
    //设置调试串口
    setLogPort(2);
}

//获取系统时间戳(毫秒)
uint32_t getSystemTick(void)
{
    static uint32_t tick_counter = 0;
    tick_counter++;
    return tick_counter;
}

//获取CO2传感器数据
status_t getCO2Data(uint16_t *co2_ppm)
{
    status_t status = STATUS_ERROR;
    char *co2_str = NULL;
    char *ppm_str = NULL;

    do {
        //发送CO2请求指令到传感器
        UART_DRV_SendDataBlocking(INST_UART5_SAMPLE, CO2_PREFIX, strlen(CO2_PREFIX), TRANSMIT_TIMEOUT);
        //接收CO2传感器的报文
        status = receiveUartData(INST_UART5_SAMPLE);
        if (status == STATUS_SUCCESS) {
            //解析CO2数据 格式: "CO2:1200ppm\r\n"
            co2_str = strstr((char *)buffer, CO2_RESPONSE);
            if (co2_str != NULL) {
                co2_str += strlen(CO2_RESPONSE); // 跳过"CO2:"
                ppm_str = strstr(co2_str, CO2_SUFFIX);
                if (ppm_str != NULL) {
                    *ppm_str = '\0'; // 截断字符串
                    *co2_ppm = (uint16_t)atoi(co2_str);
                    *ppm_str = 'p'; // 恢复字符
                    printf("CO2: %dppm\n", *co2_ppm);
                    return STATUS_SUCCESS;
                }
            }
        }
    } while (0); // 只尝试一次，失败则返回错误

    *co2_ppm = 0;
    return STATUS_ERROR;
}

//解析温湿度字符串获取浮点数
void sht30_gethumtem(uint8_t *str, float *tem, float *hum)
{
    // 1. 将 uint8_t 数组转换为字符串（假设 str 以 '\0' 结尾）
    char *data = (char *)str;

    // 2. 解析湿度（格式: xxx.x）
    char *hum_str = strstr(data, SHT30_HUM) + strlen(SHT30_HUM); // 跳过 "R:"
    char *rh_sep  = strstr(hum_str, SHT30_TEM);                  // 找到 "RH "
    if (rh_sep) {
        *rh_sep = '\0';          // 截断字符串，提取湿度部分
        *hum    = atof(hum_str); // 直接用 atof 解析浮点数
        *rh_sep = ' ';           // 恢复分隔符
    } else {
        *hum = 0.0; // 解析失败
    }

    // 3. 解析温度（格式: yyy.y）
    char *tem_str = rh_sep + strlen(SHT30_TEM); // 跳过 "RH "
    *tem          = atof(tem_str);

    // 4. 调试输出
    printf("tem: %.1f, hum: %.1f\n", *tem, *hum);
}

//继电器控制函数 - 控制安全箱电磁锁
status_t safetyBoxControl(bool open)
{
    if (open) {
        PINS_DRV_WritePin(RELAY_PORT, RELAY_PIN, RELAY_ON);
        printf("Safety Box OPENED\n");
    } else {
        PINS_DRV_WritePin(RELAY_PORT, RELAY_PIN, RELAY_OFF);
        printf("Safety Box CLOSED\n");
    }
    return STATUS_SUCCESS;
}

//检查报警条件
bool checkAlarmCondition(sensor_data_t *data)
{
    if (data->temperature >= TEMP_ALARM_THRESHOLD) {
        printf("ALARM: Temperature %.1f°C >= %.1f°C\n", data->temperature, TEMP_ALARM_THRESHOLD);
        return true;
    }

    if (data->co2_ppm >= CO2_ALARM_THRESHOLD) {
        printf("ALARM: CO2 %dppm >= %dppm\n", data->co2_ppm, CO2_ALARM_THRESHOLD);
        return true;
    }

    return false;
}

//报警管理器
void alarmManager(void)
{
    uint32_t current_time = getSystemTick();

    // 检查是否需要触发报警
    if (!g_alarm_manager.is_alarm && checkAlarmCondition(&g_sensor_data)) {
        // 触发报警
        g_alarm_manager.is_alarm = true;
        g_alarm_manager.alarm_start_time = current_time;
        g_alarm_manager.led_state = true;
        g_alarm_manager.last_led_toggle = current_time;

        // 立即打开安全箱
        safetyBoxControl(true);

        // 立即上报报警信息
        g_last_mqtt_report = 0; // 强制立即上报

        printf("ALARM TRIGGERED!\n");
    }

    // 检查报警是否需要复位
    if (g_alarm_manager.is_alarm) {
        if ((current_time - g_alarm_manager.alarm_start_time) >= g_alarm_manager.alarm_duration) {
            // 6小时到达，复位报警
            g_alarm_manager.is_alarm = false;
            g_alarm_manager.led_state = false;

            // 关闭安全箱
            safetyBoxControl(false);

            // 关闭LED
            PINS_DRV_WritePin(PORTC, 28, GPIO_PIN_LOW);

            printf("ALARM RESET after 6 hours\n");
        } else {
            // 处理LED闪烁
            if ((current_time - g_alarm_manager.last_led_toggle) >= LED_BLINK_INTERVAL) {
                g_alarm_manager.led_state = !g_alarm_manager.led_state;
                g_alarm_manager.last_led_toggle = current_time;

                // 控制LED3闪烁
                PINS_DRV_WritePin(PORTC, 28, g_alarm_manager.led_state ? GPIO_PIN_HIGH : GPIO_PIN_LOW);
            }
        }
    }
}

//封装串口接收函数
status_t receiveUartData(uint32_t intance)
{
    status_t status = STATUS_ERROR;
    uint32_t bytesRemaining;

    //清除buffer数组
    memset(buffer, 0, BUFFER_SIZE);
    //接收数据
    UART_DRV_ReceiveData(intance, buffer, 1U);
    //等待接收完成
    while (UART_DRV_GetReceiveStatus(intance, &bytesRemaining) == STATUS_BUSY)
        ;
    //获取接收状态
    status = UART_DRV_GetReceiveStatus(intance, &bytesRemaining);
    //判断接收状态
    if (status == STATUS_SUCCESS)
        return status;
    return status;
}

status_t sendmessageToHF018(int location, char *str)
{
    status_t status = STATUS_ERROR;
    uint32_t bytesRemaining;
    uint8_t sendbuffer[BUFFER_SIZE];
    //清空数组sendbuffer
    memset(sendbuffer, 0, BUFFER_SIZE);
    //组织屏幕显示报文指令SET_TXT
    snprintf(sendbuffer, sizeof(sendbuffer), "SET_TXT(%d,'%s');\r\n", location, str);
    do {
        //发送指令到屏幕
        UART_DRV_SendDataBlocking(INST_UART1_SAMPLE, sendbuffer, strlen(sendbuffer), TRANSMIT_TIMEOUT);
        //接收屏幕回应
        status = receiveUartData(INST_UART1_SAMPLE);
        //判断是否接受到OK，接收不到就一直发直到接收到为止
        if (status == STATUS_SUCCESS && strncmp((char *)buffer, SEND_SUCCESS, 2) == 0)
            break;
    } while (1);
    return status;
}

//WIFE模块AT指令
status_t wifeATcommand(char *command, char *report)
{
    uint8_t flag    = -1;
    status_t status = STATUS_ERROR;
    memset(Atvuffer, 0, BUFFER_SIZE);
    do {
        //发送指令到WIFE模块
        UART_DRV_SendDataBlocking(INST_UART4_SAMPLE, command, strlen(command), TRANSMIT_TIMEOUT);
        //接收WIFE模块回应
        UART_DRV_ReceiveDataBlocking(INST_UART4_SAMPLE, Atvuffer, sizeof(Atvuffer), RECEIVE_AT_TIMEOUT);
        printf("wifeATcommand UART_DRV_ReceiveDataBlocking : %s \n", Atvuffer);
        //判断是否接受到需要的回应，接收不到就一直发直到接收到为止
        flag = strstr((char *)buffer, report);
        if (flag >= 0) {
            status == STATUS_SUCCESS;
            break;
        }
    } while (1);
    return status;
}

status_t mqttconnect_init()
{
    status_t status = STATUS_ERROR;
    char mqttcomment[BUFFER_SIZE];
    char ssidstr[MQTT_IP_LEN];
    char ipstr[MQTT_SSIC_LEN];
    char *star = NULL;
    char *end  = NULL;

    memset(mqttcomment, 0, BUFFER_SIZE);
    memset(ssidstr, 0, MQTT_IP_LEN);
    memset(ipstr, 0, MQTT_SSIC_LEN);
    //发送+++到WIFE模块，进入到指令模式
    wifeATcommand(HC25_CODE, HC25_CODE_REPORT);

    //发送AT+WSLK到WIFE模块，获取SSIC报文
    wifeATcommand(MQTT_HC25_SSIC, MQTT_HC25_SSIC_REPORT);
    //解析接收到的报文获取需要的SSIC
    star = strstr((char *)Atvuffer, MQTT_SSIC_PRE) + strlen(MQTT_SSIC_PRE);
    end  = strstr((char *)star, MQTT_SSIC_END);
    *end = '\0';
    memcpy(ssidstr, star, MQTT_SSIC_LEN);
    //发送SSID报文到屏幕
    sendmessageToHF018(HF018_SSIC_ID, ssidstr);

    //发送AT+WANN到WIFE模块，获取IP报文
    wifeATcommand(MQTT_HC25_IP, MQTT_HC25_IP_REPORT);
    //解析接收到的报文获取需要的IP
    star = strstr((char *)Atvuffer, MQTT_IP) + strlen(MQTT_IP);
    end  = strstr((char *)Atvuffer, "\r\n");
    *end = '\0';
    memcpy(ipstr, star, MQTT_IP_LEN);
    //发送IP报文到屏幕
    sendmessageToHF018(HF018_IP_ID, ipstr);

    //发送AT+ENTM到WIFE模块，退出指令模式
    wifeATcommand(HC25_EXIT, HC25_EXIT_REPORT);

    return status;
}

//获取温湿度模块的数据
status_t getsht30_gethumtemstr()
{
    status_t status = STATUS_ERROR;
    do {
        //发送Read\r\n到温湿度模块，获取温湿度报文
        UART_DRV_SendDataBlocking(INST_UART5_SAMPLE, SHT30_PREFIX, strlen(SHT30_PREFIX), TRANSMIT_TIMEOUT);
        //接收温湿度模块的报文
        status = receiveUartData(INST_UART5_SAMPLE);
        if (status == STATUS_SUCCESS)
            break;
    } while (0); // 只尝试一次
    return status;
}

//采集所有传感器数据
status_t collectSensorData(sensor_data_t *data)
{
    status_t status = STATUS_ERROR;
    float temp = 0.0f, hum = 0.0f;
    uint16_t co2 = 0;

    // 采集温湿度数据
    status = getsht30_gethumtemstr();
    if (status == STATUS_SUCCESS) {
        sht30_gethumtem(buffer, &temp, &hum);
        data->temperature = temp;
        data->humidity = hum;
    } else {
        printf("Failed to get temperature/humidity data\n");
        return STATUS_ERROR;
    }

    // 延时100ms后采集CO2数据
    OS_DelayMs(SENSOR_POLL_INTERVAL);

    status = getCO2Data(&co2);
    if (status == STATUS_SUCCESS) {
        data->co2_ppm = co2;
    } else {
        printf("Failed to get CO2 data\n");
        // CO2传感器失败不影响整体功能，设置为0
        data->co2_ppm = 0;
    }

    data->timestamp = getSystemTick();

    printf("Sensor Data: T=%.1f°C, H=%.1f%%RH, CO2=%dppm\n",
           data->temperature, data->humidity, data->co2_ppm);

    return STATUS_SUCCESS;
}

status_t sendDataToMqttBroker(sensor_data_t *data)
{
    char mqttbuffer[BUFFER_SIZE];
    char *safety_status = g_alarm_manager.is_alarm ? "opened" : "closed";
    char *alarm_status = g_alarm_manager.is_alarm ? "alarm" : "normal";

    //组织MQTT数据报文 - JSON格式包含完整传感器数据
    //报文结构:
    // - services: 服务数组，包含设备提供的所有服务
    //   - service_id: 服务标识符 "safetyBoxMonitor"，标识安全箱监测服务
    //   - properties: 属性对象，包含传感器的实时数据
    //     - temperature: 温度值(摄氏度)，保留1位小数
    //     - humidity: 湿度值(相对湿度%)，保留1位小数
    //     - co2: CO2浓度值(ppm)
    //     - safetyBoxStatus: 安全箱状态 (opened/closed)
    //     - alarmStatus: 报警状态 (normal/alarm)
    //此JSON格式符合IoT平台标准，用于设备数据上报
    snprintf(mqttbuffer, sizeof(mqttbuffer),
             "{"
             "  \"services\": ["
             "    {"
             "      \"service_id\": \"safetyBoxMonitor\","
             "      \"properties\": {"
             "        \"temperature\": %.1f,"
             "        \"humidity\": %.1f,"
             "        \"co2\": %d,"
             "        \"safetyBoxStatus\": \"%s\","
             "        \"alarmStatus\": \"%s\""
             "      }"
             "    }"
             "  ]"
             "}",
             data->temperature, data->humidity, data->co2_ppm, safety_status, alarm_status);

    //发送数据到WIFE模块
    UART_DRV_SendDataBlocking(INST_UART4_SAMPLE, mqttbuffer, strlen(mqttbuffer), TRANSMIT_TIMEOUT);
    printf("MQTT Data Sent: T=%.1f, H=%.1f, CO2=%d, Box=%s, Alarm=%s\n",
           data->temperature, data->humidity, data->co2_ppm, safety_status, alarm_status);
    return STATUS_SUCCESS;
}

status_t displayData(sensor_data_t *data)
{
    status_t status = STATUS_SUCCESS;
    char str[BUFFER_SIZE];

    //显示温度
    sprintf(str, "%.1f°C", data->temperature);
    status = sendmessageToHF018(HF018_TUM_ID, str);
    if (status != STATUS_SUCCESS) return status;
    OS_DelayMs(200);

    //显示湿度
    sprintf(str, "%.1f%%RH", data->humidity);
    status = sendmessageToHF018(HF018_HUM_ID, str);
    if (status != STATUS_SUCCESS) return status;
    OS_DelayMs(200);

    //显示CO2浓度
    sprintf(str, "%dppm", data->co2_ppm);
    status = sendmessageToHF018(HF018_CO2_ID, str);
    if (status != STATUS_SUCCESS) return status;
    OS_DelayMs(200);

    //显示安全箱状态和报警信息
    if (g_alarm_manager.is_alarm) {
        if (data->temperature >= TEMP_ALARM_THRESHOLD) {
            sprintf(str, "TEMP ALARM!");
        } else if (data->co2_ppm >= CO2_ALARM_THRESHOLD) {
            sprintf(str, "CO2 ALARM!");
        } else {
            sprintf(str, "ALARM!");
        }
    } else {
        sprintf(str, "NORMAL");
    }
    status = sendmessageToHF018(HF018_STATUS_ID, str);

    return status;
}

//在JSON字符串中查找指定键对应的值，简单辅助函数
char *find_json_value(const char *json, const char *key)
{
    // 静态缓冲区用于存储结果，需注意线程安全问题
    static char result[BUFFER_SIZE];
    char search_key[BUFFER_SIZE];

    // 构建搜索模式："key":"
    snprintf(search_key, sizeof(search_key), "\"%s\":\"", key);

    // 定位键名位置
    char *start = strstr(json, search_key);
    if (start == NULL) {
        result[0] = '\0'; // 未找到键，返回空字符串
        return result;
    }

    // 跳过键名和冒号引号，指向值的起始位置
    start += strlen(search_key);

    // 查找值的结束引号
    char *end = strchr(start, '"');
    if (end == NULL) {
        result[0] = '\0'; // 格式错误，返回空字符串
        return result;
    }

    // 计算值的长度并复制（不包含引号）
    int len = end - start;
    if (len >= BUFFER_SIZE) {
        len = BUFFER_SIZE - 1; // 防止缓冲区溢出
    }
    strncpy(result, start, len);
    result[len] = '\0'; // 手动添加字符串结束符

    return result;
}

//LED操作函数
status_t solveled(char *led_num, char *led_opt)
{
    //判断是不是LED3
    if (strncmp(led_num, "LED3", 4) == 0) {
        //判断开
        if (strncmp(led_opt, "ON", 2) == 0) {
            PINS_DRV_WritePin(PORTC, 28, GPIO_PIN_HIGH);
            return STATUS_SUCCESS;
        }
        //判断关
        else if (strncmp(led_opt, "OFF", 3) == 0) {
            PINS_DRV_WritePin(PORTC, 28, GPIO_PIN_LOW);
            return STATUS_SUCCESS;
        }
    }
    //判断是不是LED4
    else if (strncmp(led_num, "LED4", 4) == 0) {
        //判断开
        if (strncmp(led_opt, "ON", 2) == 0) {
            PINS_DRV_WritePin(PORTD, 14, GPIO_PIN_HIGH);
            return STATUS_SUCCESS;
        }
        //判断关
        else if (strncmp(led_opt, "OFF", 3) == 0) {
            PINS_DRV_WritePin(PORTD, 14, GPIO_PIN_LOW);
            return STATUS_SUCCESS;
        }
    }
    return STATUS_ERROR;
}

//解析接收到的JSON报文
status_t SolveCommand(char *str)
{
    status_t status = STATUS_ERROR;
    char combuffer[BUFFER_SIZE];
    memset(combuffer, 0, BUFFER_SIZE);
    strncpy(combuffer, str, BUFFER_SIZE - 1);
    char *led_num_opt = NULL;
    char led_num[BUFFER_SIZE];
    char led_opt[BUFFER_SIZE];

    //解析JSON报文获取LED编号
    led_num_opt = find_json_value(combuffer, LED_NUMBER);
    strncpy(led_num, led_num_opt, BUFFER_SIZE - 1);

    //解析JSON报文获取LED操作
    led_num_opt = find_json_value(combuffer, LED_ACTION);
    strncpy(led_opt, led_num_opt, BUFFER_SIZE - 1);

    //执行LED操作
    status = solveled(led_num, led_opt);
    return status;
}

//接收WIFE模块发送过来的MQTT指令
status_t MQTTcommand()
{
    status_t status = STATUS_ERROR;
    uint8_t *commandbuffer[BUFFER_SIZE];
    memset(commandbuffer, 0, BUFFER_SIZE);
    //接收数据,超时退出
    UART_DRV_ReceiveDataBlocking(INST_UART4_SAMPLE, commandbuffer, BUFFER_SIZE, RECEIVE_TIMEOUT);
    //判断是否接收到数据
    if (strncmp(commandbuffer, "\0\0\0", 3)) {
        //解析接收到的JSON报文并执行
        status = SolveCommand((char *)commandbuffer);
        return status;
    } else {
        return STATUS_ERROR;
    }
}

int main(void)
{
    status_t status;
    uint32_t current_time;

    //系统时钟初始化
    CLOCK_SYS_Init(g_pstClockManConfigsArr[0]);

    //引脚初始化
    PINS_DRV_Init(NUM_OF_CONFIGURED_PINS, g_stPinmuxConfigArr);

    //串口初始化
    eswin_uart_init();

    //初始化继电器引脚(确保安全箱初始状态为关闭)
    safetyBoxControl(false);

    //等屏幕开机
    OS_DelayMs(6000);
    UART_DRV_SendDataBlocking(INST_UART1_SAMPLE, HF018_JUMP, strlen(HF018_JUMP), TRANSMIT_TIMEOUT);
    OS_DelayMs(2000);

    //MQTT连接初始化
    mqttconnect_init();

    //初始化时间戳
    g_last_mqtt_report = getSystemTick();
    g_last_sensor_poll = getSystemTick();

    printf("Safety Box Monitor System Started\n");
    printf("Temperature Alarm Threshold: %.1f°C\n", TEMP_ALARM_THRESHOLD);
    printf("CO2 Alarm Threshold: %dppm\n", CO2_ALARM_THRESHOLD);

    while (1) {
        current_time = getSystemTick();

        //传感器数据采集(每100ms采集一次)
        if ((current_time - g_last_sensor_poll) >= SENSOR_POLL_INTERVAL) {
            status = collectSensorData(&g_sensor_data);
            if (status == STATUS_SUCCESS) {
                //更新屏幕显示
                status = displayData(&g_sensor_data);
                if (status == STATUS_SUCCESS) {
                    printf("Display updated successfully\n");
                }
            } else {
                printf("Sensor data collection failed, entering standby\n");
                g_system_state = SYSTEM_STANDBY;
            }
            g_last_sensor_poll = current_time;
        }

        //报警管理
        alarmManager();

        //MQTT数据上报(6小时上报一次，或报警时立即上报)
        if ((current_time - g_last_mqtt_report) >= MQTT_REPORT_INTERVAL ||
            (g_alarm_manager.is_alarm && g_last_mqtt_report != 0)) {

            if (g_sensor_data.timestamp > 0) { // 确保有有效数据
                status = sendDataToMqttBroker(&g_sensor_data);
                if (status == STATUS_SUCCESS) {
                    printf("MQTT data reported successfully\n");
                    g_last_mqtt_report = current_time;
                }
            }
        }

        //接收MQTT指令(保持原有远程控制功能)
        status = MQTTcommand();
        if (status == STATUS_ERROR) {
            // MQTT指令接收失败是正常的(超时)，不打印错误
        } else if (status == STATUS_SUCCESS) {
            printf("MQTT command executed successfully\n");
        }

        //短暂延时，避免CPU占用过高
        OS_DelayMs(10);
    }
}

// LED操作json格式
// {
//     "led": "LED3",
//     "action": "ON"
// }
// {
//     "led": "LED3",
//     "action": "OFF"
// }
// {
//     "led": "LED4",
//     "action": "ON"
// }
// {
//     "led": "LED4",
//     "action": "OFF"
// }
